(this.webpackJsonpredux_test=this.webpackJsonpredux_test||[]).push([[0],{29:function(e,t,n){"use strict";n.r(t);var r=n(1),c=n(0),o=n(8),s=n.n(o),i=n(3),a=n(4),u=n(7),d=n(6),l="increment",j="decrement",b="add_person",p=function(e){return{type:l,data:e}},h=n(5),O=function(e){Object(u.a)(n,e);var t=Object(d.a)(n);function n(){var e;Object(i.a)(this,n);for(var r=arguments.length,c=new Array(r),o=0;o<r;o++)c[o]=arguments[o];return(e=t.call.apply(t,[this].concat(c))).state={carName:"\u5954\u9a70c63"},e.increment=function(){var t=e.selectNumber.value;e.props.increment(1*t)},e.decrement=function(){var t=e.selectNumber.value;e.props.decrement(1*t)},e.incrementIfOdd=function(){var t=e.selectNumber.value;e.props.count%2!==0&&e.props.increment(1*t)},e.incrementAsync=function(){var t=e.selectNumber.value;e.props.incrementAsync(1*t,500)},e}return Object(a.a)(n,[{key:"render",value:function(){var e=this;return Object(r.jsxs)("div",{children:[Object(r.jsxs)("h2",{children:["\u6211\u662fCount\u7ec4\u4ef6,\u4e0b\u65b9\u7ec4\u4ef6\u603b\u4eba\u6570\u4e3a:",this.props.renshu]}),Object(r.jsxs)("h4",{children:["\u5f53\u524d\u6c42\u548c\u4e3a\uff1a",this.props.count]}),Object(r.jsxs)("select",{ref:function(t){return e.selectNumber=t},children:[Object(r.jsx)("option",{value:"1",children:"1"}),Object(r.jsx)("option",{value:"2",children:"2"}),Object(r.jsx)("option",{value:"3",children:"3"})]}),"\xa0",Object(r.jsx)("button",{onClick:this.increment,children:"+"}),"\xa0",Object(r.jsx)("button",{onClick:this.decrement,children:"-"}),"\xa0",Object(r.jsx)("button",{onClick:this.incrementIfOdd,children:"\u5f53\u524d\u6c42\u548c\u4e3a\u5947\u6570\u518d\u52a0"}),"\xa0",Object(r.jsx)("button",{onClick:this.incrementAsync,children:"\u5f02\u6b65\u52a0"}),"\xa0"]})}}]),n}(c.Component),v=Object(h.b)((function(e){return{count:e.count,personCount:e.persons.length}}),{increment:p,decrement:function(e){return{type:j,data:e}},incrementAsync:function(e,t){return function(n){setTimeout((function(){n(p(e))}),t)}}})(O),m=n(17),f=function(e){Object(u.a)(n,e);var t=Object(d.a)(n);function n(){var e;Object(i.a)(this,n);for(var r=arguments.length,c=new Array(r),o=0;o<r;o++)c[o]=arguments[o];return(e=t.call.apply(t,[this].concat(c))).addPerson=function(){var t=e.nameNode.value,n=1*e.ageNode.value,r={id:Object(m.a)(),name:t,age:n};e.props.addPerson(r),e.nameNode.value="",e.ageNode.value=""},e}return Object(a.a)(n,[{key:"render",value:function(){var e=this;return Object(r.jsxs)("div",{children:[Object(r.jsxs)("h2",{children:["\u6211\u662fPerson\u7ec4\u4ef6,\u4e0a\u65b9\u7ec4\u4ef6\u6c42\u548c\u4e3a",this.props.count]}),Object(r.jsx)("input",{ref:function(t){return e.nameNode=t},type:"text",placeholder:"\u8f93\u5165\u540d\u5b57"}),Object(r.jsx)("input",{ref:function(t){return e.ageNode=t},type:"text",placeholder:"\u8f93\u5165\u5e74\u9f84"}),Object(r.jsx)("button",{onClick:this.addPerson,children:"\u6dfb\u52a0"}),Object(r.jsx)("ul",{children:this.props.persons.map((function(e){return Object(r.jsxs)("li",{children:[e.name,"--",e.age]},e.id)}))})]})}}]),n}(c.Component),x=Object(h.b)((function(e){return{persons:e.persons,count:e.count}}),{addPerson:function(e){return{type:b,data:e}}})(f),y=function(e){Object(u.a)(n,e);var t=Object(d.a)(n);function n(){return Object(i.a)(this,n),t.apply(this,arguments)}return Object(a.a)(n,[{key:"render",value:function(){return Object(r.jsxs)("div",{children:[Object(r.jsx)(v,{}),Object(r.jsx)("hr",{}),Object(r.jsx)(x,{})]})}}]),n}(c.Component),g=n(2);var N=n(16),k=[{id:"001",name:"tom",age:18}];var C=Object(g.combineReducers)({count:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1?arguments[1]:void 0,n=t.type,r=t.data;switch(n){case l:return e+r;case j:return e-r;default:return e}},persons:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:k,t=arguments.length>1?arguments[1]:void 0,n=t.type,r=t.data;switch(n){case b:return[r].concat(Object(N.a)(e));default:return e}}}),w=n(14),A=n(15),P=Object(g.createStore)(C,Object(A.composeWithDevTools)(Object(g.applyMiddleware)(w.a)));s.a.render(Object(r.jsx)(h.a,{store:P,children:Object(r.jsx)(y,{})}),document.getElementById("root"))}},[[29,1,2]]]);
//# sourceMappingURL=main.d1379770.chunk.js.map