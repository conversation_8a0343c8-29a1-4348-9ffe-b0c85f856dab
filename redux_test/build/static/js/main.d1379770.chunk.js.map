{"version": 3, "sources": ["redux/constant.js", "redux/actions/count.js", "containers/Count/index.jsx", "containers/Person/index.jsx", "redux/actions/person.js", "App.jsx", "redux/reducers/person.js", "redux/reducers/index.js", "redux/reducers/count.js", "redux/store.js", "index.js"], "names": ["INCREMENT", "DECREMENT", "ADD_PERSON", "increment", "data", "type", "Count", "state", "carName", "value", "selectNumber", "props", "decrement", "incrementIfOdd", "count", "incrementAsync", "this", "renshu", "ref", "c", "onClick", "Component", "connect", "personCount", "persons", "length", "time", "dispatch", "setTimeout", "Person", "add<PERSON><PERSON>", "name", "nameNode", "age", "ageNode", "<PERSON><PERSON><PERSON><PERSON>", "id", "nanoid", "placeholder", "map", "p", "App", "initState", "combineReducers", "preState", "action", "createStore", "reducer", "composeWithDevTools", "applyMiddleware", "thunk", "ReactDOM", "render", "store", "document", "getElementById"], "mappings": "8KAGaA,EAAY,YACZC,EAAY,YACZC,EAAa,aCCbC,EAAY,SAAAC,GAAI,MAAK,CAACC,KAAKL,EAAUI,S,OCK5CE,E,4MAELC,MAAQ,CAACC,QAAQ,mB,EAGjBL,UAAY,WAAK,IACTM,EAAS,EAAKC,aAAdD,MACP,EAAKE,MAAMR,UAAgB,EAANM,I,EAGtBG,UAAY,WAAK,IACTH,EAAS,EAAKC,aAAdD,MACP,EAAKE,MAAMC,UAAgB,EAANH,I,EAGtBI,eAAiB,WAAK,IACdJ,EAAS,EAAKC,aAAdD,MACJ,EAAKE,MAAMG,MAAQ,IAAM,GAC3B,EAAKH,MAAMR,UAAgB,EAANM,I,EAIvBM,eAAiB,WAAK,IACdN,EAAS,EAAKC,aAAdD,MACP,EAAKE,MAAMI,eAAqB,EAANN,EAAQ,M,uDAGzB,IAAD,OAER,OACC,gCACC,iHAAwBO,KAAKL,MAAMM,UACnC,sEAAWD,KAAKL,MAAMG,SACtB,yBAAQI,IAAK,SAAAC,GAAC,OAAI,EAAKT,aAAeS,GAAtC,UACC,wBAAQV,MAAM,IAAd,eACA,wBAAQA,MAAM,IAAd,eACA,wBAAQA,MAAM,IAAd,kBANF,OAQC,wBAAQW,QAASJ,KAAKb,UAAtB,eARD,OASC,wBAAQiB,QAASJ,KAAKJ,UAAtB,eATD,OAUC,wBAAQQ,QAASJ,KAAKH,eAAtB,oEAVD,OAWC,wBAAQO,QAASJ,KAAKD,eAAtB,gCAXD,c,GA9BiBM,aAgDLC,eACd,SAAAf,GAAK,MAAK,CACTO,MAAMP,EAAMO,MACZS,YAAYhB,EAAMiB,QAAQC,UAE3B,CAACtB,YAAUS,UDzDa,SAAAR,GAAI,MAAK,CAACC,KAAKJ,EAAUG,SCyD5BW,eDtDQ,SAACX,EAAKsB,GACnC,OAAO,SAACC,GACPC,YAAW,WACVD,EAASxB,EAAUC,MAClBsB,MC6CWJ,CAMbhB,G,QC5DIuB,E,4MAELC,UAAY,WACX,IAAMC,EAAO,EAAKC,SAASvB,MACrBwB,EAAyB,EAAnB,EAAKC,QAAQzB,MACnB0B,EAAY,CAACC,GAAGC,cAASN,OAAKE,OACpC,EAAKtB,MAAMmB,UAAUK,GACrB,EAAKH,SAASvB,MAAQ,GACtB,EAAKyB,QAAQzB,MAAQ,I,uDAGZ,IAAD,OACR,OACC,gCACC,2GAAuBO,KAAKL,MAAMG,SAClC,uBAAOI,IAAK,SAAAC,GAAC,OAAE,EAAKa,SAAWb,GAAGd,KAAK,OAAOiC,YAAY,6BAC1D,uBAAOpB,IAAK,SAAAC,GAAC,OAAE,EAAKe,QAAUf,GAAGd,KAAK,OAAOiC,YAAY,6BACzD,wBAAQlB,QAASJ,KAAKc,UAAtB,0BACA,6BAEEd,KAAKL,MAAMa,QAAQe,KAAI,SAACC,GACvB,OAAO,+BAAgBA,EAAET,KAAlB,KAA0BS,EAAEP,MAAnBO,EAAEJ,gB,GArBJf,aA8BNC,eACd,SAAAf,GAAK,MAAK,CACTiB,QAAQjB,EAAMiB,QACdV,MAAMP,EAAMO,SAEb,CAACgB,UCrCuB,SAAAK,GAAS,MAAK,CAAC9B,KAAKH,EAAWE,KAAK+B,KDgC9Cb,CAMbO,GErCmBY,E,uKAEnB,OACC,gCACC,cAAC,EAAD,IACA,uBACA,cAAC,EAAD,W,GAN6BpB,a,mBCD3BqB,EAAY,CAAC,CAACN,GAAG,MAAML,KAAK,MAAME,IAAI,KCQ5BU,gCAAgB,CAC/B7B,MCLc,WAAiD,IAA3B8B,EAA0B,uDAD7C,EACsCC,EAAO,uCAGvDxC,EAAawC,EAAbxC,KAAKD,EAAQyC,EAARzC,KAEZ,OAAQC,GACP,KAAKL,EACJ,OAAO4C,EAAWxC,EACnB,KAAKH,EACJ,OAAO2C,EAAWxC,EACnB,QACC,OAAOwC,IDLTpB,QDRc,WAAkD,IAA3BoB,EAA0B,uDAAjBF,EAAUG,EAAO,uCAExDxC,EAAawC,EAAbxC,KAAKD,EAAQyC,EAARzC,KACZ,OAAQC,GACP,KAAKH,EAEJ,MAAM,CAAEE,GAAR,mBAAgBwC,IACjB,QACC,OAAOA,M,gBGCKE,wBAAYC,EAAQC,8BAAoBC,0BAAgBC,OCRvEC,IAASC,OAER,cAAC,IAAD,CAAUC,MAAOA,EAAjB,SACC,cAAC,EAAD,MAEDC,SAASC,eAAe,W", "file": "static/js/main.d1379770.chunk.js", "sourcesContent": ["/* \r\n\t该模块是用于定义action对象中type类型的常量值，目的只有一个：便于管理的同时防止程序员单词写错\r\n*/\r\nexport const INCREMENT = 'increment'\r\nexport const DECREMENT = 'decrement'\r\nexport const ADD_PERSON = 'add_person'", "/* \r\n\t该文件专门为Count组件生成action对象\r\n*/\r\nimport {INCREMENT,DECREMENT} from '../constant'\r\n\r\n//同步action，就是指action的值为Object类型的一般对象\r\nexport const increment = data => ({type:INCREMENT,data})\r\nexport const decrement = data => ({type:DECREMENT,data})\r\n\r\n//异步action，就是指action的值为函数,异步action中一般都会调用同步action，异步action不是必须要用的。\r\nexport const incrementAsync = (data,time) => {\r\n\treturn (dispatch)=>{\r\n\t\tsetTimeout(()=>{\r\n\t\t\tdispatch(increment(data))\r\n\t\t},time)\r\n\t}\r\n}", "import React, { Component } from 'react'\r\n//引入action\r\nimport {\r\n\tincrement,\r\n\tdecrement,\r\n\tincrementAsync\r\n} from '../../redux/actions/count'\r\n//引入connect用于连接UI组件与redux\r\nimport {connect} from 'react-redux'\r\n\r\n//定义UI组件\r\nclass Count extends Component {\r\n\r\n\tstate = {carName:'奔驰c63'}\r\n\r\n\t//加法\r\n\tincrement = ()=>{\r\n\t\tconst {value} = this.selectNumber\r\n\t\tthis.props.increment(value*1)\r\n\t}\r\n\t//减法\r\n\tdecrement = ()=>{\r\n\t\tconst {value} = this.selectNumber\r\n\t\tthis.props.decrement(value*1)\r\n\t}\r\n\t//奇数再加\r\n\tincrementIfOdd = ()=>{\r\n\t\tconst {value} = this.selectNumber\r\n\t\tif(this.props.count % 2 !== 0){\r\n\t\t\tthis.props.increment(value*1)\r\n\t\t}\r\n\t}\r\n\t//异步加\r\n\tincrementAsync = ()=>{\r\n\t\tconst {value} = this.selectNumber\r\n\t\tthis.props.incrementAsync(value*1,500)\r\n\t}\r\n\r\n\trender() {\r\n\t\t//console.log('UI组件接收到的props是',this.props);\r\n\t\treturn (\r\n\t\t\t<div>\r\n\t\t\t\t<h2>我是Count组件,下方组件总人数为:{this.props.renshu}</h2>\r\n\t\t\t\t<h4>当前求和为：{this.props.count}</h4>\r\n\t\t\t\t<select ref={c => this.selectNumber = c}>\r\n\t\t\t\t\t<option value=\"1\">1</option>\r\n\t\t\t\t\t<option value=\"2\">2</option>\r\n\t\t\t\t\t<option value=\"3\">3</option>\r\n\t\t\t\t</select>&nbsp;\r\n\t\t\t\t<button onClick={this.increment}>+</button>&nbsp;\r\n\t\t\t\t<button onClick={this.decrement}>-</button>&nbsp;\r\n\t\t\t\t<button onClick={this.incrementIfOdd}>当前求和为奇数再加</button>&nbsp;\r\n\t\t\t\t<button onClick={this.incrementAsync}>异步加</button>&nbsp;\r\n\t\t\t</div>\r\n\t\t)\r\n\t}\r\n}\r\n\r\n//使用connect()()创建并暴露一个Count的容器组件\r\nexport default connect(\r\n\tstate => ({\r\n\t\tcount:state.count,\r\n\t\tpersonCount:state.persons.length\r\n\t}),\r\n\t{increment,decrement,incrementAsync}\r\n)(Count)\r\n\r\n", "import React, { Component } from 'react'\r\nimport {nanoid} from 'nanoid'\r\nimport {connect} from 'react-redux'\r\nimport {addPerson} from '../../redux/actions/person'\r\n\r\nclass Person extends Component {\r\n\r\n\taddPerson = ()=>{\r\n\t\tconst name = this.nameNode.value\r\n\t\tconst age = this.ageNode.value*1\r\n\t\tconst personObj = {id:nanoid(),name,age}\r\n\t\tthis.props.addPerson(personObj)\r\n\t\tthis.nameNode.value = ''\r\n\t\tthis.ageNode.value = ''\r\n\t}\r\n\r\n\trender() {\r\n\t\treturn (\r\n\t\t\t<div>\r\n\t\t\t\t<h2>我是Person组件,上方组件求和为{this.props.count}</h2>\r\n\t\t\t\t<input ref={c=>this.nameNode = c} type=\"text\" placeholder=\"输入名字\"/>\r\n\t\t\t\t<input ref={c=>this.ageNode = c} type=\"text\" placeholder=\"输入年龄\"/>\r\n\t\t\t\t<button onClick={this.addPerson}>添加</button>\r\n\t\t\t\t<ul>\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tthis.props.persons.map((p)=>{\r\n\t\t\t\t\t\t\treturn <li key={p.id}>{p.name}--{p.age}</li>\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t</ul>\r\n\t\t\t</div>\r\n\t\t)\r\n\t}\r\n}\r\n\r\nexport default connect(\r\n\tstate => ({\r\n\t\tpersons:state.persons,\r\n\t\tcount:state.count\r\n\t}),//映射状态\r\n\t{addPerson}//映射操作状态的方法\r\n)(Person)\r\n", "import {ADD_PERSON} from '../constant'\r\n\r\n//创建增加一个人的action动作对象\r\nexport const addPerson = personObj => ({type:ADD_PERSON,data:personObj})", "import React, { Component } from 'react'\r\nimport Count from './containers/Count' //引入的Count的容器组件\r\nimport Person from './containers/Person' //引入的Person的容器组件\r\n\r\nexport default class App extends Component {\r\n\trender() {\r\n\t\treturn (\r\n\t\t\t<div>\r\n\t\t\t\t<Count/>\r\n\t\t\t\t<hr/>\r\n\t\t\t\t<Person/>\r\n\t\t\t</div>\r\n\t\t)\r\n\t}\r\n}\r\n", "import {ADD_PERSON} from '../constant'\r\n\r\n//初始化人的列表\r\nconst initState = [{id:'001',name:'tom',age:18}]\r\n\r\nexport default function personReducer(preState=initState,action){\r\n\t// console.log('personReducer@#@#@#');\r\n\tconst {type,data} = action\r\n\tswitch (type) {\r\n\t\tcase ADD_PERSON: //若是添加一个人\r\n\t\t\t//preState.unshift(data) //此处不可以这样写，这样会导致preState被改写了，personReducer就不是纯函数了。\r\n\t\t\treturn [data,...preState]\r\n\t\tdefault:\r\n\t\t\treturn preState\r\n\t}\r\n}\r\n", "/* \r\n\t该文件用于汇总所有的reducer为一个总的reducer\r\n*/\r\n//引入combineReducers，用于汇总多个reducer\r\nimport {combineReducers} from 'redux'\r\n//引入为Count组件服务的reducer\r\nimport count from './count'\r\n//引入为Person组件服务的reducer\r\nimport persons from './person'\r\n\r\n//汇总所有的reducer变为一个总的reducer\r\nexport default  combineReducers({\r\n\tcount,\r\n\tpersons\r\n})\r\n", "/* \r\n\t1.该文件是用于创建一个为Count组件服务的reducer，reducer的本质就是一个函数\r\n\t2.reducer函数会接到两个参数，分别为：之前的状态(preState)，动作对象(action)\r\n*/\r\nimport {INCREMENT,DECREMENT} from '../constant'\r\n\r\nconst initState = 0 //初始化状态\r\nexport default function countReducer(preState=initState,action){\r\n\t// console.log('countReducer@#@#@#');\r\n\t//从action对象中获取：type、data\r\n\tconst {type,data} = action\r\n\t//根据type决定如何加工数据\r\n\tswitch (type) {\r\n\t\tcase INCREMENT: //如果是加\r\n\t\t\treturn preState + data\r\n\t\tcase DECREMENT: //若果是减\r\n\t\t\treturn preState - data\r\n\t\tdefault:\r\n\t\t\treturn preState\r\n\t}\r\n}", "/* \r\n\t该文件专门用于暴露一个store对象，整个应用只有一个store对象\r\n*/\r\n\r\n//引入createStore，专门用于创建redux中最为核心的store对象\r\nimport {createStore,applyMiddleware} from 'redux'\r\n//引入汇总之后的reducer\r\nimport reducer from './reducers'\r\n//引入redux-thunk，用于支持异步action\r\nimport thunk from 'redux-thunk'\r\n//引入redux-devtools-extension\r\nimport {composeWithDevTools} from 'redux-devtools-extension'\r\n\r\n//暴露store \r\nexport default createStore(reducer,composeWithDevTools(applyMiddleware(thunk)))", "import React from 'react'\r\nimport ReactDOM from 'react-dom'\r\nimport App from './App'\r\nimport store from './redux/store'\r\nimport {Provider} from 'react-redux'\r\n\r\nReactDOM.render(\r\n\t/* 此处需要用Provider包裹App，目的是让App所有的后代容器组件都能接收到store */\r\n\t<Provider store={store}>\r\n\t\t<App/>\r\n\t</Provider>,\r\n\tdocument.getElementById('root')\r\n)"], "sourceRoot": ""}