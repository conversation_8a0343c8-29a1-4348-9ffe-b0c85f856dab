[{"C:\\Users\\<USER>\\Desktop\\react_extension\\src\\App.js": "1", "C:\\Users\\<USER>\\Desktop\\react_extension\\src\\components\\5_Context\\index.jsx": "2", "C:\\Users\\<USER>\\Desktop\\react_extension\\src\\components\\1_setState\\index.jsx": "3", "C:\\Users\\<USER>\\Desktop\\react_extension\\src\\components\\6_optimize\\index.jsx": "4", "C:\\Users\\<USER>\\Desktop\\react_extension\\src\\components\\7_renderProps\\index.jsx": "5", "C:\\Users\\<USER>\\Desktop\\react_extension\\src\\components\\8_ErrorBoundary\\Child.jsx": "6", "C:\\Users\\<USER>\\Desktop\\react_extension\\src\\components\\8_ErrorBoundary\\Parent.jsx": "7"}, {"size": 229, "mtime": 1606964499753, "results": "8", "hashOfConfig": "9"}, {"size": 1201, "mtime": 1606638252693, "results": "10", "hashOfConfig": "9"}, {"size": 624, "mtime": 1606624131709, "results": "11", "hashOfConfig": "9"}, {"size": 1737, "mtime": 1606960279788, "results": "12", "hashOfConfig": "9"}, {"size": 720, "mtime": 1606962480131, "results": "13", "hashOfConfig": "9"}, {"size": 473, "mtime": 1606964789956, "results": "14", "hashOfConfig": "9"}, {"size": 660, "mtime": 1606965394111, "results": "15", "hashOfConfig": "9"}, {"filePath": "16", "messages": "17", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "86soag", {"filePath": "18", "messages": "19", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "20", "messages": "21", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "22", "messages": "23", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "26"}, {"filePath": "27", "messages": "28", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\react_extension\\src\\App.js", [], "C:\\Users\\<USER>\\Desktop\\react_extension\\src\\components\\5_Context\\index.jsx", [], "C:\\Users\\<USER>\\Desktop\\react_extension\\src\\components\\1_setState\\index.jsx", [], "C:\\Users\\<USER>\\Desktop\\react_extension\\src\\components\\6_optimize\\index.jsx", [], "C:\\Users\\<USER>\\Desktop\\react_extension\\src\\components\\7_renderProps\\index.jsx", ["31"], "import React, { Component } from 'react'\r\nimport './index.css'\r\nimport C from '../1_setState'\r\n\r\nexport default class Parent extends Component {\r\n\trender() {\r\n\t\treturn (\r\n\t\t\t<div className=\"parent\">\r\n\t\t\t\t<h3>我是Parent组件</h3>\r\n\t\t\t\t<A render={(name)=><C name={name}/>}/>\r\n\t\t\t</div>\r\n\t\t)\r\n\t}\r\n}\r\n\r\nclass A extends Component {\r\n\tstate = {name:'tom'}\r\n\trender() {\r\n\t\tconsole.log(this.props);\r\n\t\tconst {name} = this.state\r\n\t\treturn (\r\n\t\t\t<div className=\"a\">\r\n\t\t\t\t<h3>我是A组件</h3>\r\n\t\t\t\t{this.props.render(name)}\r\n\t\t\t</div>\r\n\t\t)\r\n\t}\r\n}\r\n\r\nclass B extends Component {\r\n\trender() {\r\n\t\tconsole.log('B--render');\r\n\t\treturn (\r\n\t\t\t<div className=\"b\">\r\n\t\t\t\t<h3>我是B组件,{this.props.name}</h3>\r\n\t\t\t</div>\r\n\t\t)\r\n\t}\r\n}\r\n", "C:\\Users\\<USER>\\Desktop\\react_extension\\src\\components\\8_ErrorBoundary\\Child.jsx", [], "C:\\Users\\<USER>\\Desktop\\react_extension\\src\\components\\8_ErrorBoundary\\Parent.jsx", [], {"ruleId": "32", "severity": 1, "message": "33", "line": 30, "column": 7, "nodeType": "34", "messageId": "35", "endLine": 30, "endColumn": 8}, "no-unused-vars", "'B' is defined but never used.", "Identifier", "unusedVar"]