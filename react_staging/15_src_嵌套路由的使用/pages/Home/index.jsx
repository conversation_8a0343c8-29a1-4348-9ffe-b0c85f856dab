import React, { Component } from 'react'
import MyNavLink from '../../components/MyNavLink'
import {Route,Switch,Redirect} from 'react-router-dom'
import News from './News'
import Message from './Message'

export default class Home extends Component {
	render() {
		return (
				<div>
					<h3>我是Home的内容</h3>
					<div>
						<ul className="nav nav-tabs">
							<li>
								<MyNavLink to="/home/<USER>">News</MyNavLink>
							</li>
							<li>
								<MyNavLink to="/home/<USER>">Message</MyNavLink>
							</li>
						</ul>
						{/* 注册路由 */}
						<Switch>
							<Route path="/home/<USER>" component={News}/>
							<Route path="/home/<USER>" component={Message}/>
							<Redirect to="/home/<USER>"/>
						</Switch>
					</div>
				</div>
			)
	}
}
