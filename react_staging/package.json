{"name": "react_staging", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.11.4", "@testing-library/react": "^11.1.0", "@testing-library/user-event": "^12.1.10", "antd": "^4.8.2", "axios": "^0.21.0", "babel-plugin-import": "^1.13.1", "customize-cra": "^1.0.0", "less": "^3.12.2", "less-loader": "^7.1.0", "nanoid": "^3.1.16", "prop-types": "^15.7.2", "pubsub-js": "^1.9.0", "react": "^17.0.1", "react-app-rewired": "^2.1.6", "react-dom": "^17.0.1", "react-router-dom": "^5.2.0", "react-scripts": "4.0.0", "web-vitals": "^0.2.4"}, "scripts": {"start": "react-app-rewired start", "build": "react-app-rewired build", "test": "react-app-rewired test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}